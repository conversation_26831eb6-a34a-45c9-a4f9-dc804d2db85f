/**
 * Ensure Anonymous User Exists
 * 
 * This script ensures the anonymous user exists for GDPR scans.
 * Can be run independently or as part of the application startup.
 */

const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';

async function ensureAnonymousUser() {
  let knex;
  
  try {
    // Import knex dynamically to handle different environments
    try {
      knex = require('./src/lib/db').default;
    } catch (importError) {
      // Fallback to direct database connection
      const { Client } = require('pg');
      const client = new Client({
        host: process.env.POSTGRES_HOST || 'localhost',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'complychecker_dev',
        user: process.env.POSTGRES_USER || 'complyuser',
        password: process.env.POSTGRES_PASSWORD || 'complypassword',
      });
      
      await client.connect();
      
      // Check if user exists
      const existingUser = await client.query(
        'SELECT id FROM users WHERE id = $1',
        [ANONYMOUS_USER_UUID]
      );
      
      if (existingUser.rows.length === 0) {
        console.log('📝 Creating anonymous user...');
        await client.query(`
          INSERT INTO users (id, keycloak_id, email, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [ANONYMOUS_USER_UUID, 'anonymous-user-keycloak-id', '<EMAIL>']);
        console.log('✅ Anonymous user created successfully');
      } else {
        console.log('✅ Anonymous user already exists');
      }
      
      await client.end();
      return;
    }
    
    // Using knex
    console.log('🔍 Checking if anonymous user exists...');
    const existingUser = await knex('users')
      .where('id', ANONYMOUS_USER_UUID)
      .first();
    
    if (!existingUser) {
      console.log('📝 Creating anonymous user...');
      await knex('users').insert({
        id: ANONYMOUS_USER_UUID,
        keycloak_id: 'anonymous-user-keycloak-id',
        email: '<EMAIL>',
        created_at: new Date(),
        updated_at: new Date(),
      });
      console.log('✅ Anonymous user created successfully');
    } else {
      console.log('✅ Anonymous user already exists');
    }
    
  } catch (error) {
    if (error.code === '23505') {
      console.log('✅ Anonymous user already exists (duplicate key)');
    } else {
      console.error('❌ Error ensuring anonymous user:', error.message);
      throw error;
    }
  }
}

// Export for use in other modules
module.exports = { ensureAnonymousUser, ANONYMOUS_USER_UUID };

// Run directly if this file is executed
if (require.main === module) {
  ensureAnonymousUser()
    .then(() => {
      console.log('🎉 Anonymous user check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to ensure anonymous user:', error);
      process.exit(1);
    });
}

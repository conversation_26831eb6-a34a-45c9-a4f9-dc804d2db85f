// Fix anonymous user using existing database connection
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// Import the existing database connection
const db = require('./src/lib/db').default;

async function createAnonymousUser() {
  const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';
  
  try {
    console.log('🔗 Using existing database connection...');
    
    // Check if anonymous user already exists
    const existingUser = await db('users')
      .where('id', ANONYMOUS_USER_UUID)
      .first();
    
    if (existingUser) {
      console.log('✅ Anonymous user already exists');
      return;
    }
    
    // Create anonymous user
    console.log('📝 Creating anonymous user...');
    await db('users').insert({
      id: ANONYMOUS_USER_UUID,
      email: '<EMAIL>',
      username: 'anonymous',
      first_name: 'Anonymous',
      last_name: 'User',
      is_active: true,
      is_verified: false,
      role: 'user',
      created_at: new Date(),
      updated_at: new Date(),
    });
    
    console.log('✅ Anonymous user created successfully!');
    console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
    console.log('   GDPR scans will now work without foreign key errors');
    
  } catch (error) {
    console.error('❌ Error creating anonymous user:', error);
    throw error;
  } finally {
    // Close database connection
    await db.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
createAnonymousUser()
  .then(() => {
    console.log('\n🎉 Anonymous user setup completed!');
    console.log('You can now run GDPR scans without database errors.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Failed to create anonymous user:', error);
    process.exit(1);
  });

# Fix Anonymous User for GDPR Scans

## 🚨 ISSUE
```
Key (user_id)=(00000000-0000-0000-0000-000000000000) is not present in table "users"
```

## ✅ SOLUTION

The anonymous user with UUID `00000000-0000-0000-0000-000000000000` needs to be created in the database.

### **Option 1: Direct SQL Command** (Recommended)

Connect to your PostgreSQL database and run this SQL:

```sql
INSERT INTO users (
    id, 
    email, 
    username, 
    first_name, 
    last_name, 
    is_active, 
    is_verified, 
    role, 
    created_at, 
    updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000000', 
    '<EMAIL>', 
    'anonymous', 
    'Anonymous', 
    'User', 
    true, 
    false, 
    'user', 
    NOW(), 
    NOW()
) ON CONFLICT (id) DO NOTHING;
```

### **Option 2: Using psql Command Line**

```bash
psql -h localhost -U complyuser -d complychecker_dev -c "
INSERT INTO users (
    id, email, username, first_name, last_name, 
    is_active, is_verified, role, created_at, updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'anonymous',
    'Anonymous',
    'User',
    true,
    false,
    'user',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;
"
```

### **Option 3: Using Database GUI**

If you're using a database GUI (like pgAdmin, DBeaver, etc.):

1. Connect to database: `complychecker_dev`
2. Open SQL query window
3. Paste and execute the SQL from Option 1

### **Option 4: Using Docker** (if database is in Docker)

```bash
docker exec -it <postgres_container_name> psql -U complyuser -d complychecker_dev -c "
INSERT INTO users (
    id, email, username, first_name, last_name, 
    is_active, is_verified, role, created_at, updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'anonymous',
    'Anonymous',
    'User',
    true,
    false,
    'user',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;
"
```

## 🔍 VERIFICATION

After running the SQL, verify the user was created:

```sql
SELECT id, email, username FROM users WHERE id = '00000000-0000-0000-0000-000000000000';
```

Expected result:
```
                  id                  |            email             | username  
--------------------------------------+------------------------------+-----------
 00000000-0000-0000-0000-000000000000 | <EMAIL> | anonymous
```

## 🧪 TEST GDPR SCAN

After creating the anonymous user:

1. Restart your backend server
2. Go to GDPR dashboard
3. Click "Start GDPR Compliance Scan"
4. Should work without foreign key errors

## 📋 DATABASE CREDENTIALS

Based on your `.env` file:
- **Host**: localhost
- **Port**: 5432
- **Database**: complychecker_dev
- **User**: complyuser
- **Password**: complypassword

## ⚠️ IMPORTANT NOTES

- **One-time setup**: Only needs to be done once
- **Safe operation**: Uses `ON CONFLICT DO NOTHING` to prevent duplicates
- **Required for anonymous scans**: All GDPR scans from unauthenticated users need this user record
- **No impact on existing data**: This only adds the missing user record

Once this anonymous user is created, your GDPR scans will work perfectly without any foreign key constraint violations!

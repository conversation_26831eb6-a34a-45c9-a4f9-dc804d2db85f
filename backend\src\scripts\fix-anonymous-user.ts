/**
 * Fix anonymous user for GDPR scans
 * Run with: npx ts-node -r tsconfig-paths/register src/scripts/fix-anonymous-user.ts
 */

import db from '../lib/db';

const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';

async function createAnonymousUser() {
  try {
    console.log('🔗 Connecting to database...');
    
    // Check if anonymous user already exists
    const existingUser = await db('users')
      .where('id', ANONYMOUS_USER_UUID)
      .first();
    
    if (existingUser) {
      console.log('✅ Anonymous user already exists');
      console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
      console.log(`   Email: ${existingUser.email}`);
      return;
    }
    
    // Create anonymous user
    console.log('📝 Creating anonymous user...');
    await db('users').insert({
      id: ANONYMOUS_USER_UUID,
      email: '<EMAIL>',
      username: 'anonymous',
      first_name: 'Anonymous',
      last_name: 'User',
      is_active: true,
      is_verified: false,
      role: 'user',
      created_at: new Date(),
      updated_at: new Date(),
    });
    
    console.log('✅ Anonymous user created successfully!');
    console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
    console.log(`   Email: <EMAIL>`);
    console.log('   GDPR scans will now work without foreign key errors');
    
  } catch (error) {
    console.error('❌ Error creating anonymous user:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('connect')) {
        console.error('💡 Database connection failed. Make sure PostgreSQL is running.');
      } else if (error.message.includes('does not exist')) {
        console.error('💡 Database or table does not exist. Run migrations first.');
      }
    }
    
    throw error;
  } finally {
    // Close database connection
    await db.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
async function main() {
  try {
    await createAnonymousUser();
    console.log('\n🎉 Anonymous user setup completed!');
    console.log('You can now run GDPR scans without database errors.');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Failed to create anonymous user');
    console.error('Please check database connection and try again.');
    process.exit(1);
  }
}

main();

/**
 * GDPR Frontend Types
 *
 * TypeScript types for GDPR compliance frontend components.
 * These types mirror the backend types but are optimized for frontend use.
 */

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: {
    message: string;
    code?: string;
    details?: unknown;
  };
  metadata?: {
    timestamp: string;
    requestId?: string;
  };
}

// Core GDPR Types
export type GdprRuleId = 
  | 'GDPR-001' | 'GDPR-002' | 'GDPR-003' | 'GDPR-004' | 'GDPR-005'
  | 'GDPR-006' | 'GDPR-007' | 'GDPR-008' | 'GDPR-009' | 'GDPR-010'
  | 'GDPR-011' | 'GDPR-012' | 'GDPR-013' | 'GDPR-014' | 'GDPR-015'
  | 'GDPR-016' | 'GDPR-017' | 'GDPR-018' | 'GDPR-019' | 'GDPR-020'
  | 'GDPR-021';

export type GdprCategory = 
  | 'security' | 'privacy_policy' | 'consent' | 'cookies' 
  | 'data_rights' | 'data_protection' | 'organizational';

export type Severity = 'critical' | 'high' | 'medium' | 'low';
export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// Scan Request Interface
export interface GdprScanRequest {
  targetUrl: string;
  scanOptions?: {
    enableCookieAnalysis?: boolean;
    enableTrackerDetection?: boolean;
    enableConsentTesting?: boolean;
    maxPages?: number;
    timeout?: number;
  };
}

// Scan Result Interfaces
export interface GdprScanResult {
  scanId: string;
  targetUrl: string;
  timestamp: string;
  scanDuration: number;
  overallScore: number;
  riskLevel: RiskLevel;
  status: ScanStatus;
  summary?: GdprScanSummary; // Made optional to handle cases where summary might be undefined
  checks: GdprCheckResult[];
  recommendations: GdprRecommendation[];
  metadata?: GdprScanMetadata; // Made optional to handle cases where metadata might be undefined
}

export interface GdprScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  criticalFailures: number;
  categoryBreakdown: CategoryBreakdown[];
}

export interface CategoryBreakdown {
  category: GdprCategory;
  score: number;
  checksInCategory: number;
  passedInCategory: number;
}

export interface GdprCheckResult {
  ruleId: GdprRuleId;
  ruleName: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  evidence: Evidence[];
  recommendations: Recommendation[];
  manualReviewRequired: boolean;
  manualReviewCompleted?: boolean;
  manualReviewAssessment?: 'compliant' | 'non-compliant' | 'partially-compliant' | 'needs-review';
  manualReviewNotes?: string;
  manualReviewerName?: string;
  manualReviewedAt?: string;
  manualReviewedBy?: string;
}

export interface Evidence {
  type: 'text' | 'element' | 'network' | 'cookie';
  description: string;
  location?: string;
  value?: string;
}

export interface Recommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
}

export interface GdprRecommendation {
  id: string;
  priority: number;
  title: string;
  description: string;
  category: GdprCategory;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  relatedRules: GdprRuleId[];
}

export interface GdprScanMetadata {
  version: string;
  processingTime: number;
  checksPerformed: number;
  analysisLevelsUsed: string[];
  errors: string[];
  warnings: string[];
  userAgent: string;
  scanOptions?: GdprScanRequest['scanOptions'];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GdprScanResponse extends ApiResponse<GdprScanResult> {}

export interface GdprScanListResponse extends ApiResponse<GdprScanResult[]> {}

// Manual Review Types
export interface ManualReviewItem {
  ruleId: GdprRuleId;
  ruleName: string;
  category: GdprCategory;
  automatedFindings: Evidence[];
  reviewStatus: 'pending' | 'in_progress' | 'completed';
  reviewNotes?: string;
  reviewerAssessment?: 'compliant' | 'non_compliant' | 'needs_improvement';
  reviewDate?: string;
  reviewerId?: string;
}

export interface ManualReviewSummary {
  totalItems: number;
  pendingReview: number;
  inProgress: number;
  completed: number;
  complianceRate: number;
}

// Cookie Analysis Types
export interface CookieAnalysisResult {
  cookieId: string;
  name: string;
  domain: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  secureFlag: boolean;
  httpOnlyFlag: boolean;
  sameSiteAttribute?: string;
  expiryDate?: string;
  purpose?: string;
  thirdParty: boolean;
  complianceIssues: string[];
}

// Consent Analysis Types
export interface ConsentAnalysisResult {
  consentType: string;
  consentMechanism: string;
  hasRejectOption: boolean;
  hasGranularOptions: boolean;
  preTickedBoxes: boolean;
  consentText: string;
  privacyPolicyLinked: boolean;
  compliant: boolean;
  issues: string[];
}

// Tracker Analysis Types
export interface TrackerAnalysisResult {
  domain: string;
  name: string;
  type: string;
  loadsBeforeConsent: boolean;
  hasConsentMechanism: boolean;
  dataTransferred: string;
  privacyPolicyMentioned: boolean;
  compliant: boolean;
}

// Component Props Types
export interface GdprScanFormProps {
  onSubmit: (request: GdprScanRequest) => void;
  isLoading?: boolean;
  initialUrl?: string;
}

export interface GdprResultsProps {
  scanResult: GdprScanResult;
  onNewScan?: () => void;
}

export interface GdprCheckResultProps {
  check: GdprCheckResult;
  expanded?: boolean;
  onToggle?: () => void;
}

export interface GdprSummaryProps {
  summary: GdprScanSummary;
  overallScore: number;
  riskLevel: RiskLevel;
}

// Dashboard Types
export interface GdprDashboardData {
  recentScans: GdprScanResult[];
  totalScans: number;
  averageScore: number;
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  categoryPerformance: CategoryBreakdown[];
}

// Cookie Analysis Types
export interface Cookie {
  name: string;
  value: string;
  domain: string;
  path: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'Strict' | 'Lax' | 'None' | undefined;
  expires?: Date;
}

export interface CookieAnalysisResult {
  cookieId: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  complianceIssues: string[];
  recommendations: string[];
}

// Consent Analysis Types
export interface ConsentBannerInfo {
  isPresent: boolean;
  bannerText: string;
  hasAcceptButton: boolean;
  hasRejectButton: boolean;
  hasGranularOptions: boolean;
  privacyPolicyLinked: boolean;
  complianceIssues: string[];
}

// Tracker Analysis Types
export interface Tracker {
  domain: string;
  name: string;
  category: TrackerCategory;
  purpose: string;
  dataCollected: string[];
  hasConsentMechanism: boolean;
  loadedBeforeConsent: boolean;
}

export type TrackerCategory = 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';

// Form Validation Types
export interface GdprScanFormErrors {
  targetUrl?: string;
  scanOptions?: {
    maxPages?: string;
    timeout?: string;
  };
}

// Loading States
export interface GdprLoadingState {
  isScanning: boolean;
  isLoadingResults: boolean;
  isLoadingHistory: boolean;
  scanProgress?: number;
  currentStep?: string;
}

// Filter and Sort Types
export interface GdprScanFilters {
  riskLevel?: RiskLevel[];
  category?: GdprCategory[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  scoreRange?: {
    min: number;
    max: number;
  };
}

export interface GdprScanSortOptions {
  field: 'timestamp' | 'overallScore' | 'riskLevel' | 'targetUrl';
  direction: 'asc' | 'desc';
}

import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface PrivacyPolicyCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class PrivacyPolicyCheck {
  /**
   * Check for privacy policy presence and accessibility
   * REAL WEBSITE ANALYSIS - scans actual webpage content
   */
  async performCheck(config: PrivacyPolicyCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      // Navigate to actual website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Search for privacy policy links - REAL DOM analysis
      const privacyLinks = await this.findPrivacyPolicyLinks(page);

      if (privacyLinks.length === 0) {
        evidence.push({
          type: 'element',
          description: 'No privacy policy links found',
          location: 'Page scan',
        });
      } else {
        passed = true;

        for (const link of privacyLinks) {
          evidence.push({
            type: 'element',
            description: 'Privacy policy link found',
            location: link.location,
            value: link.text,
          });
        }

        // Verify links are accessible - REAL link validation
        const linkValidation = await this.validatePrivacyPolicyLinks(page, privacyLinks);

        if (linkValidation.accessibleLinks === 0) {
          passed = false;
          evidence.push({
            type: 'network',
            description: 'Privacy policy links are not accessible',
            value: `${linkValidation.brokenLinks} broken links found`,
          });
        }
      }

      return {
        ruleId: 'GDPR-002',
        ruleName: 'Privacy Policy Presence',
        category: 'privacy_policy',
        passed,
        score: passed ? 100 : 0,
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: passed
          ? []
          : [
              {
                priority: 1,
                title: 'Add privacy policy link',
                description:
                  'Include a clearly labeled privacy policy link in the website footer or header',
                implementation:
                  'Add link with text "Privacy Policy" or "Privacy Notice" that leads to comprehensive privacy information',
                effort: 'minimal',
                impact: 'high',
              },
            ],
        manualReviewRequired: false,
      };
    } catch (error) {
      return {
        ruleId: 'GDPR-002',
        ruleName: 'Privacy Policy Presence',
        category: 'privacy_policy',
        passed: false,
        score: 0,
        weight: 7,
        severity: 'high',
        evidence: [
          {
            type: 'text',
            description: 'Check failed with error',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Fix website accessibility',
            description: 'Ensure website is accessible for privacy policy scanning',
            implementation: 'Check website loading and accessibility issues',
            effort: 'moderate',
            impact: 'medium',
          },
        ],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Find privacy policy links on the page - REAL DOM scanning
   */
  private async findPrivacyPolicyLinks(page: Page): Promise<
    Array<{
      text: string;
      href: string;
      location: string;
    }>
  > {
    return await page.evaluate(() => {
      const links: Array<{ text: string; href: string; location: string }> = [];

      // Search for privacy policy related links
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /privacy\s*statement/i,
        /data\s*protection/i,
        /gdpr/i,
      ];

      const allLinks = document.querySelectorAll('a[href]');

      allLinks.forEach((link, index) => {
        const text = link.textContent?.trim() || '';
        const href = (link as HTMLAnchorElement).href;

        // Check if link text matches privacy patterns
        const matchesPattern = privacyPatterns.some((pattern) => pattern.test(text));

        // Also check href for privacy-related paths
        const hrefMatchesPattern = privacyPatterns.some((pattern) => pattern.test(href));

        if (matchesPattern || hrefMatchesPattern) {
          // Determine location context
          let location = 'Unknown';
          const parent = link.closest('footer, header, nav, .footer, .header, .navigation');
          if (parent) {
            location = parent.tagName.toLowerCase();
          }

          links.push({
            text,
            href,
            location: `${location} (link ${index + 1})`,
          });
        }
      });

      return links;
    });
  }

  /**
   * Validate privacy policy links accessibility - REAL link testing
   */
  private async validatePrivacyPolicyLinks(
    page: Page,
    links: Array<{ href: string; text: string }>,
  ): Promise<{
    accessibleLinks: number;
    brokenLinks: number;
  }> {
    let accessibleLinks = 0;
    let brokenLinks = 0;

    for (const link of links) {
      try {
        // Test if link is accessible
        const response = await page.goto(link.href, {
          waitUntil: 'networkidle2',
          timeout: 10000,
        });

        if (response && response.status() < 400) {
          accessibleLinks++;
        } else {
          brokenLinks++;
        }
      } catch (error) {
        brokenLinks++;
      }
    }

    return { accessibleLinks, brokenLinks };
  }
}

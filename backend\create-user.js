// Simple script to create anonymous user
const { Client } = require('pg');

async function createUser() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'complychecker_dev',
    user: 'complyuser',
    password: 'complypassword',
  });

  try {
    await client.connect();
    console.log('Connected to database');

    const result = await client.query(`
      INSERT INTO users (
        id, email, username, first_name, last_name, 
        is_active, is_verified, role, created_at, updated_at
      ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        '<EMAIL>',
        'anonymous',
        'Anonymous',
        'User',
        true,
        false,
        'user',
        NOW(),
        NOW()
      ) ON CONFLICT (id) DO NOTHING
      RETURNING id;
    `);

    if (result.rows.length > 0) {
      console.log('✅ Anonymous user created successfully');
    } else {
      console.log('✅ Anonymous user already exists');
    }

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await client.end();
  }
}

createUser();

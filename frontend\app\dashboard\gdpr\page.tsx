'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GdprScanForm } from '@/components/gdpr/GdprScanForm';
import { GdprResultsDisplay } from '@/components/gdpr/GdprResultsDisplay';
import { ManualReviewDashboard } from '@/components/gdpr/ManualReviewDashboard';
import { GdprApiService } from '@/services/gdpr-api';
import { GdprScanResult } from '@/types/gdpr';
import { <PERSON>, <PERSON><PERSON>, Eye, FileText, AlertTriangle, CheckCircle } from 'lucide-react';

export default function GdprDashboard() {
  const [recentScans, setRecentScans] = useState<GdprScanResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadRecentScans();
  }, []);

  const loadRecentScans = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🔄 Loading GDPR scan history...');

      // Load REAL scan history - NO MOCK DATA
      const scans = await GdprApiService.getScanHistory(10, 0);

      // Ensure scans is always an array to prevent reduce errors
      if (Array.isArray(scans)) {
        console.log('📊 Loaded GDPR scans:', scans.length, 'scans');
        console.log('📊 First scan data:', scans[0]);
        setRecentScans(scans);

        if (scans.length === 0) {
          console.log('ℹ️ No GDPR scans found for current user');
        }
      } else {
        console.warn('API returned non-array data:', scans);
        setRecentScans([]);
        setError('Invalid data format received from server');
      }
    } catch (error) {
      console.error('❌ Failed to load recent scans:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack
        });
      }

      setError(error instanceof Error ? error.message : 'Failed to load scan history');
      // Ensure recentScans is always an array even on error
      setRecentScans([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleScanComplete = (scanResult: GdprScanResult) => {
    console.log('🎉 Scan completed:', scanResult);
    console.log('🎉 Overall score:', scanResult.overallScore);
    console.log('🎉 Summary:', scanResult.summary);
    // Add new scan to the beginning of the list
    setRecentScans((prev) => [scanResult, ...prev.slice(0, 9)]);
    setActiveTab('results');

    // Refresh the scan history to get the latest data from the server
    setTimeout(() => {
      loadRecentScans();
    }, 1000);
  };

  const handleReviewUpdate = (scanId: string, updatedScore: number) => {
    console.log('📝 Manual review updated for scan:', scanId, 'New score:', updatedScore);

    // Update the specific scan in the recent scans list
    setRecentScans((prev) =>
      prev.map((scan) =>
        scan.scanId === scanId
          ? { ...scan, overallScore: updatedScore }
          : scan
      )
    );

    // Refresh the scan history to get the latest data from the server
    setTimeout(() => {
      loadRecentScans();
    }, 2000);
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <Eye className="h-4 w-4" />;
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading GDPR dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">GDPR Compliance</h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive GDPR compliance scanning and analysis
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          <Shield className="h-4 w-4 mr-2" />
          21 Compliance Rules
        </Badge>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="scan">New Scan</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="manual-review">Manual Review</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{recentScans.length}</div>
                <p className="text-xs text-muted-foreground">GDPR compliance scans performed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Array.isArray(recentScans) && recentScans.length > 0
                    ? Math.round(
                        recentScans.reduce((sum, scan) => sum + (scan?.overallScore || 0), 0) /
                          recentScans.length,
                      )
                    : 0}
                  %
                </div>
                <p className="text-xs text-muted-foreground">Across all scans</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Manual Reviews</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Array.isArray(recentScans)
                    ? recentScans.reduce((sum, scan) => sum + (scan?.summary?.manualReviewRequired || 0), 0)
                    : 0}
                </div>
                <p className="text-xs text-muted-foreground">Items requiring manual review</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cookie Issues</CardTitle>
                <Cookie className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Array.isArray(recentScans)
                    ? recentScans.filter((scan) =>
                        Array.isArray(scan?.checks) &&
                        scan.checks.some((check) => check?.category === 'cookies' && !check?.passed),
                      ).length
                    : 0}
                </div>
                <p className="text-xs text-muted-foreground">Scans with cookie compliance issues</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Scans */}
          <Card>
            <CardHeader>
              <CardTitle>Recent GDPR Scans</CardTitle>
              <CardDescription>Latest compliance scans with real-time results</CardDescription>
            </CardHeader>
            <CardContent>
              {!Array.isArray(recentScans) || recentScans.length === 0 ? (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No GDPR scans yet</p>
                  <p className="text-sm text-muted-foreground">
                    Start your first scan to see results here
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentScans.slice(0, 5).map((scan) => (
                    <div
                      key={scan.scanId}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${getRiskLevelColor(scan.riskLevel)}`}>
                          {getRiskLevelIcon(scan.riskLevel)}
                        </div>
                        <div>
                          <p className="font-medium">{scan.targetUrl || 'Unknown URL'}</p>
                          <p className="text-sm text-muted-foreground">
                            {scan.timestamp ? new Date(scan.timestamp).toLocaleDateString() : 'Unknown Date'} • Score:{' '}
                            {scan.overallScore || 0}% • {scan.summary?.manualReviewRequired || 0} manual reviews
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={scan.riskLevel === 'low' ? 'default' : 'destructive'}>
                          {scan.riskLevel}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            console.log('🔍 Viewing scan details:', scan.scanId);
                            setActiveTab('results');
                            // Move this scan to the front of the array so it shows in results tab
                            setRecentScans(prev => [scan, ...prev.filter(s => s.scanId !== scan.scanId)]);
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* New Scan Tab */}
        <TabsContent value="scan">
          <GdprScanForm onScanComplete={handleScanComplete} />
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results">
          {Array.isArray(recentScans) && recentScans.length > 0 ? (
            <GdprResultsDisplay scanResult={recentScans[0]} />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No scan results available</p>
                <p className="text-sm text-muted-foreground mb-4">
                  Perform a GDPR scan to see detailed results
                </p>
                <Button onClick={() => setActiveTab('scan')}>Start New Scan</Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Manual Review Tab */}
        <TabsContent value="manual-review">
          <ManualReviewDashboard recentScans={recentScans} onReviewUpdate={handleReviewUpdate} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

/**
 * Browser Pool Manager for GDPR Compliance Checks
 * 
 * This class manages a pool of Puppeteer browser instances to prevent
 * resource exhaustion from running too many browsers simultaneously.
 * 
 * CRITICAL FIX: Prevents system resource exhaustion by limiting concurrent browsers
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

interface BrowserInstance {
  browser: Browser;
  inUse: boolean;
  lastUsed: Date;
  pageCount: number;
}

export class BrowserPoolManager {
  private static instance: BrowserPoolManager;
  private pool: BrowserInstance[] = [];
  private readonly maxBrowsers: number;
  private readonly maxPagesPerBrowser: number;
  private readonly browserTimeout: number;

  private constructor() {
    // Optimized limits for better performance and resource management
    this.maxBrowsers = 2; // Reduced to 2 browsers for better memory management
    this.maxPagesPerBrowser = 3; // Reduced pages per browser to prevent memory leaks
    this.browserTimeout = 180000; // 3 minutes timeout for faster cleanup

    console.log(`🚀 Browser pool initialized: ${this.maxBrowsers} max browsers, ${this.maxPagesPerBrowser} max pages per browser`);
  }

  public static getInstance(): BrowserPoolManager {
    if (!BrowserPoolManager.instance) {
      BrowserPoolManager.instance = new BrowserPoolManager();
    }
    return BrowserPoolManager.instance;
  }

  /**
   * Get an available browser instance or create a new one
   */
  async getBrowser(): Promise<Browser> {
    // Try to find an available browser
    let availableBrowser = this.pool.find(
      (instance) => !instance.inUse && instance.pageCount < this.maxPagesPerBrowser
    );

    if (availableBrowser) {
      availableBrowser.inUse = true;
      availableBrowser.lastUsed = new Date();
      availableBrowser.pageCount++;
      console.log(`♻️ Reusing browser instance (${this.pool.length} total)`);
      return availableBrowser.browser;
    }

    // Create new browser if under limit
    if (this.pool.length < this.maxBrowsers) {
      console.log(`🚀 Creating new browser instance (${this.pool.length + 1}/${this.maxBrowsers})`);
      
      const browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage', // Reduce memory usage
          '--disable-gpu',
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-default-apps',
          '--disable-extensions',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--memory-pressure-off', // Prevent memory pressure
          '--max-old-space-size=512', // Limit memory usage to 512MB
          '--disable-features=VizDisplayCompositor', // Reduce GPU usage
          '--disable-ipc-flooding-protection', // Improve performance
          '--disable-web-security', // Speed up loading
          '--disable-features=TranslateUI', // Disable translate
          '--disable-background-networking', // Reduce network overhead
          '--disable-sync', // Disable sync features
          '--disable-client-side-phishing-detection', // Speed up loading
        ],
        timeout: 30000, // 30 second timeout for browser launch
      });

      const instance: BrowserInstance = {
        browser,
        inUse: true,
        lastUsed: new Date(),
        pageCount: 1,
      };

      this.pool.push(instance);
      return browser;
    }

    // Wait for an available browser if at limit
    console.log(`⏳ Waiting for available browser (${this.maxBrowsers} browsers in use)`);
    return this.waitForAvailableBrowser();
  }

  /**
   * Release a browser back to the pool
   */
  async releaseBrowser(browser: Browser): Promise<void> {
    const instance = this.pool.find((inst) => inst.browser === browser);
    if (instance) {
      instance.inUse = false;
      instance.pageCount = Math.max(0, instance.pageCount - 1);
      instance.lastUsed = new Date();
      console.log(`✅ Released browser instance (pages: ${instance.pageCount})`);
    }
  }

  /**
   * Wait for an available browser instance
   */
  private async waitForAvailableBrowser(): Promise<Browser> {
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(() => {
        const availableBrowser = this.pool.find(
          (instance) => !instance.inUse && instance.pageCount < this.maxPagesPerBrowser
        );

        if (availableBrowser) {
          clearInterval(checkInterval);
          availableBrowser.inUse = true;
          availableBrowser.lastUsed = new Date();
          availableBrowser.pageCount++;
          resolve(availableBrowser.browser);
        }
      }, 1000);

      // Timeout after 30 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        reject(new Error('Timeout waiting for available browser'));
      }, 30000);
    });
  }

  /**
   * Create a new page with proper configuration
   */
  async createPage(browser: Browser): Promise<Page> {
    const page = await browser.newPage();
    
    // Configure page for GDPR scanning
    await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Set reasonable timeouts
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(60000);

    return page;
  }

  /**
   * Cleanup and close all browsers
   */
  async cleanup(): Promise<void> {
    console.log(`🧹 Cleaning up ${this.pool.length} browser instances`);
    
    const closePromises = this.pool.map(async (instance) => {
      try {
        await instance.browser.close();
      } catch (error) {
        console.error('Error closing browser:', error);
      }
    });

    await Promise.allSettled(closePromises);
    this.pool = [];
    console.log('✅ Browser pool cleanup completed');
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    totalBrowsers: number;
    activeBrowsers: number;
    availableBrowsers: number;
    totalPages: number;
  } {
    const activeBrowsers = this.pool.filter((inst) => inst.inUse).length;
    const totalPages = this.pool.reduce((sum, inst) => sum + inst.pageCount, 0);

    return {
      totalBrowsers: this.pool.length,
      activeBrowsers,
      availableBrowsers: this.pool.length - activeBrowsers,
      totalPages,
    };
  }

  /**
   * Cleanup idle browsers periodically
   */
  async cleanupIdleBrowsers(): Promise<void> {
    const now = new Date();
    const idleThreshold = 5 * 60 * 1000; // 5 minutes

    const idleBrowsers = this.pool.filter(
      (instance) => 
        !instance.inUse && 
        now.getTime() - instance.lastUsed.getTime() > idleThreshold
    );

    if (idleBrowsers.length > 0) {
      console.log(`🧹 Cleaning up ${idleBrowsers.length} idle browsers`);
      
      for (const instance of idleBrowsers) {
        try {
          await instance.browser.close();
          this.pool = this.pool.filter((inst) => inst !== instance);
        } catch (error) {
          console.error('Error closing idle browser:', error);
        }
      }
    }
  }
}

/**
 * Utility function to execute a check with browser pooling
 */
export async function withBrowserPool<T>(
  checkFunction: (browser: Browser) => Promise<T>
): Promise<T> {
  const pool = BrowserPoolManager.getInstance();
  const browser = await pool.getBrowser();
  
  try {
    const result = await checkFunction(browser);
    return result;
  } finally {
    await pool.releaseBrowser(browser);
  }
}

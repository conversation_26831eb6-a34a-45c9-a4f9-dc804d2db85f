'use client';

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import Link from 'next/link';
import { ThemeToggle } from './theme-toggle';
import { useThemeColors, useButtonStyles } from '@/hooks/useThemeColors';

const Navbar: React.FC = () => {
  const { authenticated, login, logout, profile } = useAuth();
  const colors = useThemeColors();
  const buttonStyles = useButtonStyles();

  // Navigation bar styling using theme colors
  const navStyle = {
    backgroundColor: colors.isDark ? '#1F2937' : '#374151', // Dark gray for navbar
    color: '#FFFFFF',
    borderBottom: `1px solid ${colors.border}`,
  };

  return (
    <nav className="p-4 shadow-md w-full transition-colors" style={navStyle}>
      <div className="container mx-auto flex justify-between items-center">
        <Link
          href="/"
          className="text-xl font-bold hover:opacity-80 transition-opacity"
          style={{ color: '#FFFFFF' }}
        >
          Comply Checker
        </Link>
        <div className="flex items-center gap-4">
          {/* Theme Toggle - Always visible */}
          <ThemeToggle />

          {!authenticated ? (
            <button
              onClick={() => login()}
              style={buttonStyles.primary.style}
              className={`${buttonStyles.primary.className} font-bold py-2 px-4 rounded`}
            >
              Login
            </button>
          ) : (
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard/scan/new"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-green-600 hover:bg-green-700"
              >
                New Scan
              </Link>
              <Link
                href="/dashboard/scans"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700"
              >
                My Scans
              </Link>

              {/* HIPAA Compliance Dropdown */}
              <div className="relative group">
                <button
                  className="text-sm text-white hover:text-gray-200 px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700 flex items-center"
                  style={{ backgroundColor: '#0055A4' }}
                >
                  HIPAA
                  <svg
                    className="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <Link
                      href="/dashboard/hipaa"
                      className="block px-4 py-2 text-sm font-semibold border-b border-gray-100"
                      style={{ color: '#333333' }}
                    >
                      📊 HIPAA Dashboard
                    </Link>
                    <div
                      className="px-4 py-1 text-xs font-medium uppercase tracking-wide"
                      style={{ color: '#666666' }}
                    >
                      Modules
                    </div>
                    <Link
                      href="/dashboard/hipaa/privacy"
                      className="block px-4 py-2 text-sm hover:bg-gray-100"
                      style={{ color: '#333333' }}
                    >
                      📄 Privacy Policy Compliance
                    </Link>
                    <Link
                      href="/dashboard/hipaa/security"
                      className="block px-4 py-2 text-sm hover:bg-gray-100"
                      style={{ color: '#333333' }}
                    >
                      🔒 Security Compliance
                    </Link>
                  </div>
                </div>
              </div>

              {/* GDPR Compliance Dropdown */}
              <div className="relative group">
                <button
                  className="text-sm text-white hover:text-gray-200 px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700 flex items-center"
                  style={{ backgroundColor: '#0055A4' }}
                >
                  GDPR
                  <svg
                    className="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <Link
                      href="/dashboard/gdpr"
                      className="block px-4 py-2 text-sm font-semibold border-b border-gray-100"
                      style={{ color: '#333333' }}
                    >
                      📊 GDPR Dashboard
                    </Link>
                    <div
                      className="px-4 py-1 text-xs font-medium uppercase tracking-wide"
                      style={{ color: '#666666' }}
                    >
                      Features
                    </div>
                    <Link
                      href="/dashboard/gdpr"
                      className="block px-4 py-2 text-sm hover:bg-gray-100"
                      style={{ color: '#333333' }}
                    >
                      🔍 Compliance Scan
                    </Link>
                    <Link
                      href="/dashboard/gdpr"
                      className="block px-4 py-2 text-sm hover:bg-gray-100"
                      style={{ color: '#333333' }}
                    >
                      📋 Manual Review
                    </Link>
                  </div>
                </div>
              </div>

              <Link
                href="/guidance"
                className="text-sm text-white hover:text-gray-200 px-3 py-2 rounded-md hover:opacity-90"
                style={{ backgroundColor: '#663399' }}
              >
                Guidance
              </Link>
              <span className="text-sm">
                Welcome, {profile?.firstName || profile?.username || profile?.email || 'User'}
              </span>
              <button
                onClick={() => logout()}
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;

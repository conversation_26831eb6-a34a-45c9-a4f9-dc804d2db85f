/**
 * @file Main entry point for the Comply Checker backend application.
 * This file initializes the Express server, sets up middleware (including Keycloak for authentication),
 * configures routes, and starts the HTTP server.
 * It also includes global error handling and a 404 handler for unmanaged routes.
 */
import path from 'path';
import dotenv from 'dotenv';

// Load .env file from project root for local development outside Docker
// THIS MUST BE AT THE VERY TOP BEFORE ANY OTHER IMPORTS THAT MIGHT NEED ENV VARS
if (process.env.NODE_ENV !== 'production') {
  const envPath = path.resolve(__dirname, '../../.env');
  const result = dotenv.config({ path: envPath });
  if (result.error) {
    console.warn(`[WARN] Error loading .env file from ${envPath}:`, result.error.message);
    // Optionally, throw error if .env is critical and not found,
    // but for now, we let Zod validation handle missing variables later.
  }
}

import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import http from 'http';
import { env } from '@lib/env'; // Import the Zod-validated environment variables
import mainRouter from './routes'; // Import the main router
import { sessionMiddleware, keycloak } from './lib/keycloak'; // <-- ADDED
import hipaaSecurityRoutes from './routes/hipaa-security'; // Import HIPAA Security routes
import hipaaPrivacyRoutes from './routes/hipaa-privacy'; // Import HIPAA Privacy routes
import { BrowserPoolManager } from './compliance/gdpr/utils/browser-pool'; // Import browser pool for cleanup

const app: Express = express();
const port = env.BACKEND_PORT;

console.log(`🚀 Starting Comply Checker Backend in ${env.NODE_ENV} mode`);
console.log(`📋 Port: ${port}`);

// Basic middleware for development
app.use(helmet());
app.use(
  cors({
    origin: [
      'http://localhost:3001',
      'http://localhost:3000',
      'http://localhost:8080', // Add Keycloak origin for CORS
    ],
    credentials: true,
  }),
);

// Middleware
// Apply session middleware (imported from lib/keycloak.ts)
app.use(sessionMiddleware);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Keycloak middleware (imported from lib/keycloak.ts)
// This should come AFTER session middleware and BEFORE your protected routes
app.use(
  keycloak.middleware({
    logout: '/logout', // Optional: if you want Keycloak to handle /logout
    admin: '/', // Optional: base URL for admin actions (not typically used directly by app)
  }),
); // <-- MODIFIED

// Health check endpoints
app.get('/health', (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    message: 'HIPAA Compliance Backend is running',
  });
});

app.get('/ready', (req: Request, res: Response) => {
  res.json({ ready: true, message: 'Service is ready for HIPAA analysis' });
});

// API Routes - All API routes will be prefixed with /api/v1
console.log('📋 Setting up API routes...');
app.use('/api/v1', mainRouter);

// HIPAA Security routes - Direct access for frontend integration
app.use('/api/v1/hipaa-security', hipaaSecurityRoutes);

// HIPAA Privacy routes - Direct access for frontend integration
app.use('/api/v1/hipaa-privacy', hipaaPrivacyRoutes);

// Basic root route to confirm the server is running
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Comply Checker Backend is alive!',
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    uptime: process.uptime(),
    features: ['HIPAA Compliance Analysis', '3-Level Analysis System'],
    endpoints: {
      health: '/health',
      scan: '/api/v1/compliance/scan',
      scans: '/api/v1/compliance/scans',
    },
  });
});

/**
 * Global Error Handling Middleware.
 * Catches errors from anywhere in the application pipeline.
 * Logs the error and sends a standardized JSON response.
 * In development, it includes the error message and stack trace; in production, it sends a generic error message.
 */
// This should be defined after all other app.use() and routes calls
app.use((err: Error, req: Request, res: Response, _next: NextFunction) => {
  // Mark next as unused for now
  console.error(
    `[Error] ${new Date().toISOString()} - ${req.method} ${req.originalUrl} - ${err.message}`,
  ); // Ensuring newline structure
  // Avoid sending stack trace to client in production
  const errorResponse = {
    message: env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred',
    stack: env.NODE_ENV === 'development' ? err.stack : undefined, // Added trailing comma
  };
  res.status(500).json(errorResponse);
});

/**
 * 404 Handler for unhandled routes.
 * Catches any requests that don't match a defined route and responds with a 404 status and JSON message.
 */
app.use((req: Request, res: Response) => {
  res.status(404).json({ message: 'Resource not found' });
});

let httpServer: http.Server;

if (env.NODE_ENV !== 'test') {
  console.log(`📋 Attempting to start server on port ${port}...`);
  httpServer = app
    .listen(port, () => {
      console.log('✅ Server started successfully!');
      console.log(`🌐 Server running on: http://localhost:${port}`);
      console.log(`🏥 Health check: http://localhost:${port}/health`);
      console.log(`🧪 HIPAA Analysis: http://localhost:${port}/api/v1/compliance/scan`);
      console.log(`📋 Environment: ${env.NODE_ENV}`);
      console.log('🚀 Ready to accept HIPAA compliance requests!');
    })
    .on('error', (err: NodeJS.ErrnoException) => {
      console.error('❌ Failed to start server:', err);
      if (err.code === 'EADDRINUSE') {
        console.error(
          `❌ Port ${port} is already in use. Please stop other services or use a different port.`,
        );
      }
      process.exit(1);
    });
}

// CRITICAL: Cleanup handlers for GDPR browser pool
// This prevents memory leaks and ensures proper resource cleanup
async function gracefulShutdown(signal: string) {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

  try {
    // Cleanup browser pool
    const browserPool = BrowserPoolManager.getInstance();
    await browserPool.cleanup();
    console.log('✅ Browser pool cleanup completed');

    // Close HTTP server
    if (httpServer) {
      httpServer.close(() => {
        console.log('✅ HTTP server closed');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
}

// Register cleanup handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

//  Exporting app for potential testing purposes or programmatic use, though not strictly necessary for a typical Express app.
export default app;
export { httpServer };
// export { keycloak }; // <-- REMOVED
